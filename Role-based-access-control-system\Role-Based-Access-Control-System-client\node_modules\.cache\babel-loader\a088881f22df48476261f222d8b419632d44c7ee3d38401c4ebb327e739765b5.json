{"ast": null, "code": "function isAbsolute(pathname) {\n  return pathname.charAt(0) === '/';\n}\n\n// About 1.5x faster than the two-arg version of Array#splice()\nfunction spliceOne(list, index) {\n  for (var i = index, k = i + 1, n = list.length; k < n; i += 1, k += 1) {\n    list[i] = list[k];\n  }\n  list.pop();\n}\n\n// This implementation is based heavily on node's url.parse\nfunction resolvePathname(to, from) {\n  if (from === undefined) from = '';\n  var toParts = to && to.split('/') || [];\n  var fromParts = from && from.split('/') || [];\n  var isToAbs = to && isAbsolute(to);\n  var isFromAbs = from && isAbsolute(from);\n  var mustEndAbs = isToAbs || isFromAbs;\n  if (to && isAbsolute(to)) {\n    // to is absolute\n    fromParts = toParts;\n  } else if (toParts.length) {\n    // to is relative, drop the filename\n    fromParts.pop();\n    fromParts = fromParts.concat(toParts);\n  }\n  if (!fromParts.length) return '/';\n  var hasTrailingSlash;\n  if (fromParts.length) {\n    var last = fromParts[fromParts.length - 1];\n    hasTrailingSlash = last === '.' || last === '..' || last === '';\n  } else {\n    hasTrailingSlash = false;\n  }\n  var up = 0;\n  for (var i = fromParts.length; i >= 0; i--) {\n    var part = fromParts[i];\n    if (part === '.') {\n      spliceOne(fromParts, i);\n    } else if (part === '..') {\n      spliceOne(fromParts, i);\n      up++;\n    } else if (up) {\n      spliceOne(fromParts, i);\n      up--;\n    }\n  }\n  if (!mustEndAbs) for (; up--; up) fromParts.unshift('..');\n  if (mustEndAbs && fromParts[0] !== '' && (!fromParts[0] || !isAbsolute(fromParts[0]))) fromParts.unshift('');\n  var result = fromParts.join('/');\n  if (hasTrailingSlash && result.substr(-1) !== '/') result += '/';\n  return result;\n}\nexport default resolvePathname;", "map": {"version": 3, "names": ["isAbsolute", "pathname", "char<PERSON>t", "spliceOne", "list", "index", "i", "k", "n", "length", "pop", "resolvePathname", "to", "from", "undefined", "toParts", "split", "fromParts", "isToAbs", "isFromAbs", "mustEndAbs", "concat", "hasTrailingSlash", "last", "up", "part", "unshift", "result", "join", "substr"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/Role-based-access-control-system/Role-Based-Access-Control-System-client/node_modules/resolve-pathname/esm/resolve-pathname.js"], "sourcesContent": ["function isAbsolute(pathname) {\n  return pathname.charAt(0) === '/';\n}\n\n// About 1.5x faster than the two-arg version of Array#splice()\nfunction spliceOne(list, index) {\n  for (var i = index, k = i + 1, n = list.length; k < n; i += 1, k += 1) {\n    list[i] = list[k];\n  }\n\n  list.pop();\n}\n\n// This implementation is based heavily on node's url.parse\nfunction resolvePathname(to, from) {\n  if (from === undefined) from = '';\n\n  var toParts = (to && to.split('/')) || [];\n  var fromParts = (from && from.split('/')) || [];\n\n  var isToAbs = to && isAbsolute(to);\n  var isFromAbs = from && isAbsolute(from);\n  var mustEndAbs = isToAbs || isFromAbs;\n\n  if (to && isAbsolute(to)) {\n    // to is absolute\n    fromParts = toParts;\n  } else if (toParts.length) {\n    // to is relative, drop the filename\n    fromParts.pop();\n    fromParts = fromParts.concat(toParts);\n  }\n\n  if (!fromParts.length) return '/';\n\n  var hasTrailingSlash;\n  if (fromParts.length) {\n    var last = fromParts[fromParts.length - 1];\n    hasTrailingSlash = last === '.' || last === '..' || last === '';\n  } else {\n    hasTrailingSlash = false;\n  }\n\n  var up = 0;\n  for (var i = fromParts.length; i >= 0; i--) {\n    var part = fromParts[i];\n\n    if (part === '.') {\n      spliceOne(fromParts, i);\n    } else if (part === '..') {\n      spliceOne(fromParts, i);\n      up++;\n    } else if (up) {\n      spliceOne(fromParts, i);\n      up--;\n    }\n  }\n\n  if (!mustEndAbs) for (; up--; up) fromParts.unshift('..');\n\n  if (\n    mustEndAbs &&\n    fromParts[0] !== '' &&\n    (!fromParts[0] || !isAbsolute(fromParts[0]))\n  )\n    fromParts.unshift('');\n\n  var result = fromParts.join('/');\n\n  if (hasTrailingSlash && result.substr(-1) !== '/') result += '/';\n\n  return result;\n}\n\nexport default resolvePathname;\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,QAAQ,EAAE;EAC5B,OAAOA,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;AACnC;;AAEA;AACA,SAASC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAGD,KAAK,EAAEE,CAAC,GAAGD,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE;IACrEH,IAAI,CAACE,CAAC,CAAC,GAAGF,IAAI,CAACG,CAAC,CAAC;EACnB;EAEAH,IAAI,CAACM,GAAG,CAAC,CAAC;AACZ;;AAEA;AACA,SAASC,eAAeA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACjC,IAAIA,IAAI,KAAKC,SAAS,EAAED,IAAI,GAAG,EAAE;EAEjC,IAAIE,OAAO,GAAIH,EAAE,IAAIA,EAAE,CAACI,KAAK,CAAC,GAAG,CAAC,IAAK,EAAE;EACzC,IAAIC,SAAS,GAAIJ,IAAI,IAAIA,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,IAAK,EAAE;EAE/C,IAAIE,OAAO,GAAGN,EAAE,IAAIZ,UAAU,CAACY,EAAE,CAAC;EAClC,IAAIO,SAAS,GAAGN,IAAI,IAAIb,UAAU,CAACa,IAAI,CAAC;EACxC,IAAIO,UAAU,GAAGF,OAAO,IAAIC,SAAS;EAErC,IAAIP,EAAE,IAAIZ,UAAU,CAACY,EAAE,CAAC,EAAE;IACxB;IACAK,SAAS,GAAGF,OAAO;EACrB,CAAC,MAAM,IAAIA,OAAO,CAACN,MAAM,EAAE;IACzB;IACAQ,SAAS,CAACP,GAAG,CAAC,CAAC;IACfO,SAAS,GAAGA,SAAS,CAACI,MAAM,CAACN,OAAO,CAAC;EACvC;EAEA,IAAI,CAACE,SAAS,CAACR,MAAM,EAAE,OAAO,GAAG;EAEjC,IAAIa,gBAAgB;EACpB,IAAIL,SAAS,CAACR,MAAM,EAAE;IACpB,IAAIc,IAAI,GAAGN,SAAS,CAACA,SAAS,CAACR,MAAM,GAAG,CAAC,CAAC;IAC1Ca,gBAAgB,GAAGC,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,EAAE;EACjE,CAAC,MAAM;IACLD,gBAAgB,GAAG,KAAK;EAC1B;EAEA,IAAIE,EAAE,GAAG,CAAC;EACV,KAAK,IAAIlB,CAAC,GAAGW,SAAS,CAACR,MAAM,EAAEH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,IAAImB,IAAI,GAAGR,SAAS,CAACX,CAAC,CAAC;IAEvB,IAAImB,IAAI,KAAK,GAAG,EAAE;MAChBtB,SAAS,CAACc,SAAS,EAAEX,CAAC,CAAC;IACzB,CAAC,MAAM,IAAImB,IAAI,KAAK,IAAI,EAAE;MACxBtB,SAAS,CAACc,SAAS,EAAEX,CAAC,CAAC;MACvBkB,EAAE,EAAE;IACN,CAAC,MAAM,IAAIA,EAAE,EAAE;MACbrB,SAAS,CAACc,SAAS,EAAEX,CAAC,CAAC;MACvBkB,EAAE,EAAE;IACN;EACF;EAEA,IAAI,CAACJ,UAAU,EAAE,OAAOI,EAAE,EAAE,EAAEA,EAAE,EAAEP,SAAS,CAACS,OAAO,CAAC,IAAI,CAAC;EAEzD,IACEN,UAAU,IACVH,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,KAClB,CAACA,SAAS,CAAC,CAAC,CAAC,IAAI,CAACjB,UAAU,CAACiB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAE5CA,SAAS,CAACS,OAAO,CAAC,EAAE,CAAC;EAEvB,IAAIC,MAAM,GAAGV,SAAS,CAACW,IAAI,CAAC,GAAG,CAAC;EAEhC,IAAIN,gBAAgB,IAAIK,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEF,MAAM,IAAI,GAAG;EAEhE,OAAOA,MAAM;AACf;AAEA,eAAehB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}