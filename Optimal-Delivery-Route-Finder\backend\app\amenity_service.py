"""
Amenity discovery service for route-based amenity finding
"""
import math
import requests
from typing import List, Dict, Any, Tuple, Optional
from .enhanced_route_service import calculate_distance, get_route_between_points


# Amenity type mappings for OpenStreetMap Nominatim
AMENITY_TYPE_MAPPING = {
    "restaurant": ["restaurant", "fast_food", "cafe", "food_court"],
    "gas_station": ["fuel"],
    "atm": ["atm", "bank"],
    "hospital": ["hospital", "clinic", "doctors"],
    "pharmacy": ["pharmacy"],
    "hotel": ["hotel", "motel", "guest_house"],
    "shopping": ["supermarket", "mall", "shop"],
    "school": ["school", "university", "college"],
    "government": ["townhall", "post_office", "police"],
    "worship": ["place_of_worship"]
}


def point_to_line_distance(point_lat: float, point_lng: float, 
                          line_start_lat: float, line_start_lng: float,
                          line_end_lat: float, line_end_lng: float) -> float:
    """
    Calculate the shortest distance from a point to a line segment
    Returns distance in kilometers
    """
    # Convert to radians for calculation
    def to_rad(deg):
        return deg * math.pi / 180
    
    # Earth's radius in km
    R = 6371
    
    # Convert all coordinates to radians
    lat1, lng1 = to_rad(line_start_lat), to_rad(line_start_lng)
    lat2, lng2 = to_rad(line_end_lat), to_rad(line_end_lng)
    lat3, lng3 = to_rad(point_lat), to_rad(point_lng)
    
    # Calculate cross track distance (distance from point to great circle)
    d13 = math.acos(math.sin(lat1) * math.sin(lat3) + 
                    math.cos(lat1) * math.cos(lat3) * math.cos(lng3 - lng1))
    
    bearing13 = math.atan2(math.sin(lng3 - lng1) * math.cos(lat3),
                          math.cos(lat1) * math.sin(lat3) - 
                          math.sin(lat1) * math.cos(lat3) * math.cos(lng3 - lng1))
    
    bearing12 = math.atan2(math.sin(lng2 - lng1) * math.cos(lat2),
                          math.cos(lat1) * math.sin(lat2) - 
                          math.sin(lat1) * math.cos(lat2) * math.cos(lng2 - lng1))
    
    cross_track_distance = math.asin(math.sin(d13) * math.sin(bearing13 - bearing12)) * R
    
    return abs(cross_track_distance)


def is_point_near_route(point_lat: float, point_lng: float, 
                       route_coordinates: List[List[float]], 
                       buffer_distance: float) -> Tuple[bool, float]:
    """
    Check if a point is within buffer distance of any route segment
    Returns (is_near, min_distance)
    """
    min_distance = float('inf')
    
    for i in range(len(route_coordinates) - 1):
        start_coord = route_coordinates[i]
        end_coord = route_coordinates[i + 1]
        
        distance = point_to_line_distance(
            point_lat, point_lng,
            start_coord[0], start_coord[1],
            end_coord[0], end_coord[1]
        )
        
        min_distance = min(min_distance, distance)
    
    # Convert buffer distance from meters to kilometers
    buffer_km = buffer_distance / 1000.0
    
    return min_distance <= buffer_km, min_distance


def search_amenities_nominatim(lat: float, lng: float, amenity_type: str, 
                              radius: float = 2000, limit: int = 20) -> List[Dict]:
    """
    Search for amenities using OpenStreetMap Nominatim
    """
    amenities = []
    
    # Get the OSM amenity types for this category
    osm_types = AMENITY_TYPE_MAPPING.get(amenity_type, [amenity_type])
    
    for osm_type in osm_types:
        try:
            # Search using Overpass API for better amenity results
            overpass_url = "http://overpass-api.de/api/interpreter"
            
            # Overpass query to find amenities within radius
            query = f"""
            [out:json][timeout:25];
            (
              node["amenity"="{osm_type}"](around:{radius},{lat},{lng});
              way["amenity"="{osm_type}"](around:{radius},{lat},{lng});
              relation["amenity"="{osm_type}"](around:{radius},{lat},{lng});
            );
            out center meta;
            """
            
            response = requests.post(overpass_url, data=query, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                for element in data.get("elements", []):
                    # Get coordinates
                    if element["type"] == "node":
                        elem_lat, elem_lng = element["lat"], element["lon"]
                    elif "center" in element:
                        elem_lat, elem_lng = element["center"]["lat"], element["center"]["lon"]
                    else:
                        continue
                    
                    # Get name and other details
                    tags = element.get("tags", {})
                    name = tags.get("name", f"{osm_type.title()} #{element['id']}")
                    
                    amenity_info = {
                        "id": f"osm_{element['type']}_{element['id']}",
                        "name": name,
                        "category": amenity_type,
                        "lat": elem_lat,
                        "lng": elem_lng,
                        "distance_from_search": calculate_distance(lat, lng, elem_lat, elem_lng),
                        "details": {
                            "osm_type": element["type"],
                            "osm_id": element["id"],
                            "amenity": tags.get("amenity"),
                            "opening_hours": tags.get("opening_hours"),
                            "phone": tags.get("phone"),
                            "website": tags.get("website"),
                            "address": {
                                "street": tags.get("addr:street"),
                                "city": tags.get("addr:city"),
                                "postcode": tags.get("addr:postcode")
                            }
                        }
                    }
                    
                    amenities.append(amenity_info)
                    
                    if len(amenities) >= limit:
                        break
                        
        except Exception as e:
            print(f"Error searching for {osm_type}: {e}")
            continue
    
    # Sort by distance and limit results
    amenities.sort(key=lambda x: x["distance_from_search"])
    return amenities[:limit]


def find_amenities_along_route(route_coordinates: List[List[float]], 
                              amenity_types: List[str],
                              buffer_distance: float = 500,
                              max_detour_distance: float = 2000,
                              max_results: int = 20) -> List[Dict]:
    """
    Find amenities along a route within buffer distance or detour distance
    """
    all_amenities = []
    
    # Calculate route center for search
    if not route_coordinates:
        return []
    
    center_lat = sum(coord[0] for coord in route_coordinates) / len(route_coordinates)
    center_lng = sum(coord[1] for coord in route_coordinates) / len(route_coordinates)
    
    # Search for each amenity type
    for amenity_type in amenity_types:
        amenities = search_amenities_nominatim(
            center_lat, center_lng, amenity_type, 
            radius=max_detour_distance, limit=max_results
        )
        
        for amenity in amenities:
            # Check if amenity is near the route
            is_near, distance_to_route = is_point_near_route(
                amenity["lat"], amenity["lng"], 
                route_coordinates, buffer_distance
            )
            
            amenity["distance_from_route"] = distance_to_route * 1000  # Convert to meters
            
            if is_near:
                # Amenity is within buffer - no detour needed
                amenity["estimated_detour_time"] = 0
                amenity["estimated_detour_distance"] = 0
            else:
                # Calculate detour if within max detour distance
                if distance_to_route * 1000 <= max_detour_distance:
                    # Estimate detour (simplified calculation)
                    detour_distance = distance_to_route * 2 * 1000  # Round trip in meters
                    detour_time = detour_distance / 1000 * 60  # Rough estimate: 1 km per minute
                    
                    amenity["estimated_detour_time"] = int(detour_time)
                    amenity["estimated_detour_distance"] = detour_distance
                else:
                    # Too far for detour
                    continue
            
            all_amenities.append(amenity)
    
    # Sort by distance from route and limit results
    all_amenities.sort(key=lambda x: x["distance_from_route"])
    return all_amenities[:max_results]


def find_route_amenities(route_coordinates: List[List[float]], 
                        amenity_types: List[str]) -> List[Dict]:
    """
    Simplified amenity finder for route optimization
    """
    return find_amenities_along_route(
        route_coordinates=route_coordinates,
        amenity_types=amenity_types,
        buffer_distance=500,
        max_detour_distance=2000,
        max_results=10
    )


def calculate_amenity_detour_impact(route_coordinates: List[List[float]], 
                                   amenity_lat: float, amenity_lng: float,
                                   insertion_index: int) -> Dict[str, Any]:
    """
    Calculate the impact of inserting an amenity stop at a specific index
    """
    if insertion_index < 0 or insertion_index >= len(route_coordinates):
        return {"error": "Invalid insertion index"}
    
    # Get the route segment where amenity would be inserted
    if insertion_index == 0:
        prev_coord = route_coordinates[0]
        next_coord = route_coordinates[1] if len(route_coordinates) > 1 else route_coordinates[0]
    elif insertion_index == len(route_coordinates) - 1:
        prev_coord = route_coordinates[-2] if len(route_coordinates) > 1 else route_coordinates[-1]
        next_coord = route_coordinates[-1]
    else:
        prev_coord = route_coordinates[insertion_index - 1]
        next_coord = route_coordinates[insertion_index]
    
    # Calculate original segment distance
    original_distance = calculate_distance(
        prev_coord[0], prev_coord[1], 
        next_coord[0], next_coord[1]
    )
    
    # Calculate new route distance with amenity
    distance_to_amenity = calculate_distance(
        prev_coord[0], prev_coord[1], 
        amenity_lat, amenity_lng
    )
    distance_from_amenity = calculate_distance(
        amenity_lat, amenity_lng,
        next_coord[0], next_coord[1]
    )
    
    new_distance = distance_to_amenity + distance_from_amenity
    detour_distance = new_distance - original_distance
    
    # Estimate time impact (rough calculation)
    detour_time = detour_distance * 60  # 1 km per minute estimate
    
    return {
        "detour_distance": detour_distance * 1000,  # Convert to meters
        "detour_time": int(detour_time),  # In seconds
        "insertion_index": insertion_index,
        "feasible": detour_distance <= 2.0  # Max 2km detour
    }
