'use strict';
require('../../modules/es.json.to-string-tag');
require('../../modules/es.math.to-string-tag');
require('../../modules/es.object.to-string');
require('../../modules/es.reflect.to-string-tag');
require('../../modules/es.symbol.to-string-tag');
var WrappedWellKnownSymbolModule = require('../../internals/well-known-symbol-wrapped');

module.exports = WrappedWellKnownSymbolModule.f('toStringTag');
