[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\AuthContext.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Signup.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\components\\NavBar.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Profile.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\AdminUserDashboard.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Blogs.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\AdminBlogDashboard.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\components\\BlogList.js": "12"}, {"size": 205, "mtime": 1750945450765, "results": "13", "hashOfConfig": "14"}, {"size": 1670, "mtime": 1750945450761, "results": "15", "hashOfConfig": "14"}, {"size": 1766, "mtime": 1750945450762, "results": "16", "hashOfConfig": "14"}, {"size": 352, "mtime": 1750945450762, "results": "17", "hashOfConfig": "14"}, {"size": 1579, "mtime": 1750945450768, "results": "18", "hashOfConfig": "14"}, {"size": 1930, "mtime": 1750945450770, "results": "19", "hashOfConfig": "14"}, {"size": 1292, "mtime": 1750945450765, "results": "20", "hashOfConfig": "14"}, {"size": 1611, "mtime": 1750945450769, "results": "21", "hashOfConfig": "14"}, {"size": 3813, "mtime": 1750945450767, "results": "22", "hashOfConfig": "14"}, {"size": 684, "mtime": 1750945450767, "results": "23", "hashOfConfig": "14"}, {"size": 1922, "mtime": 1750945450766, "results": "24", "hashOfConfig": "14"}, {"size": 2837, "mtime": 1750945450764, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9hfq18", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\components\\NavBar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\AdminUserDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\Blogs.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\pages\\AdminBlogDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\coding\\Role-based-access-control-system\\Role-Based-Access-Control-System-client\\src\\components\\BlogList.js", [], []]